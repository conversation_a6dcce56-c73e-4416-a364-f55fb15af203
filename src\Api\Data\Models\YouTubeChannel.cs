﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public class YouTubeChannel
{
    public required string ChannelId { get; init; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public long? SubscriberCount { get; set; }
    public int VideoCount { get; set; }
    public string? Thumbnail { get; set; }
    public required string VideosJson { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class YouTubeChannelConfiguration : IEntityTypeConfiguration<YouTubeChannel>
{
    public void Configure(EntityTypeBuilder<YouTubeChannel> builder)
    {
        builder.ToTable("youtube_channels", "public");
        builder.HasKey(c => c.ChannelId);
        builder.Property(c => c.ChannelId).IsRequired().HasMaxLength(50).HasColumnName("channel_id");
        builder.Property(c => c.Title).IsRequired().HasMaxLength(255).HasColumnName("title");
        builder.Property(c => c.Description).HasColumnType("text").HasColumnName("description");
        builder.Property(c => c.SubscriberCount).HasColumnName("subscriber_count");
        builder.Property(c => c.VideoCount).HasColumnName("video_count");
        builder.Property(c => c.Thumbnail).HasMaxLength(500).HasColumnName("thumbnail");
        builder.Property(c => c.VideosJson).IsRequired().HasColumnType("jsonb").HasColumnName("videos_json");
        builder.Property(c => c.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(c => c.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(c => c.ExpiresAt).HasColumnName("expires_at");

        builder.HasIndex(c => c.SubscriberCount).HasDatabaseName("ix_youtube_channels_subscriber_count");
        builder.HasIndex(c => c.ExpiresAt).HasDatabaseName("ix_youtube_channels_expires_at");
        builder.HasIndex(c => c.CreatedAt).HasDatabaseName("ix_youtube_channels_created_at");
    }
}