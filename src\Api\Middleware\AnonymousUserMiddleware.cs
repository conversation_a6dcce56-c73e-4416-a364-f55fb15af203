using Api.Data;
using Api.Data.Models;
using Api.Services;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Middleware;

public class AnonymousUserMiddleware
{
    public const string ANONYMOUS_COOKIE_NAME = "anonymous_id";
    public const string USER_CONTEXT_KEY = "CurrentUser";
    private readonly ILogger<AnonymousUserMiddleware> _logger;
    private readonly RequestDelegate _next;

    public AnonymousUserMiddleware(RequestDelegate next, ILogger<AnonymousUserMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, AppDbContext dbContext, AuthService authService)
    {
        try
        {
            var currentUser = await GetOrCreateCurrentUserAsync(context, dbContext, authService);

            // 将用户信息存储到HttpContext中，供后续中间件和控制器使用
            context.Items[USER_CONTEXT_KEY] = currentUser;

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication middleware error");
            await _next(context);
        }
    }

    /// <summary>
    ///     获取或创建当前用户
    /// </summary>
    private async Task<CurrentUser> GetOrCreateCurrentUserAsync(HttpContext context, AppDbContext dbContext, AuthService authService)
    {
         if (context.User.Identity == null || !context.User.Identity.IsAuthenticated)
        {
                // 2. 检查匿名用户Cookie
        if (context.Request.Cookies.TryGetValue(ANONYMOUS_COOKIE_NAME, out var anonymousId) && Guid.TryParse(anonymousId, out var anonymousUserId))
        {
            var anonymousUser = await dbContext.Users
                .Where(u => u.Id == anonymousUserId && u.UserType == UserType.Anonymous && u.Status == UserAccountStatus.Active).FirstOrDefaultAsync();

            if (anonymousUser != null)
                return new CurrentUser(anonymousUser.Id, anonymousUser.UserType, null, anonymousUser.PlanType, true);
        }

        // 3. 创建新的匿名用户
        var newAnonymousUser = new User
        {
            Id = Guid.NewGuid(),
            UserType = UserType.Anonymous,
            PlanType = UserPlanType.Free,
            Status = UserAccountStatus.Active,
            CreatedAt = DateTime.UtcNow,
            LastActiveAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        dbContext.Users.Add(newAnonymousUser);
        await dbContext.SaveChangesAsync();

        // 设置匿名用户Cookie（有效期1年）
        var cookieOptions = new CookieOptions
        {
            HttpOnly = true,
            Secure = context.Request.IsHttps,
            SameSite = SameSiteMode.Lax,
            Expires = DateTimeOffset.UtcNow.AddYears(1)
        };

        context.Response.Cookies.Append(ANONYMOUS_COOKIE_NAME, newAnonymousUser.Id.ToString(), cookieOptions);

        _logger.LogInformation("Created new anonymous user: {UserId}", newAnonymousUser.Id);

        return new CurrentUser(newAnonymousUser.Id, newAnonymousUser.UserType, null, newAnonymousUser.PlanType, true);
        }

        
    }
}

/// <summary>
///     HttpContext扩展方法，用于获取当前用户
/// </summary>
public static class HttpContextExtensions
{
    /// <summary>
    ///     获取当前用户信息
    /// </summary>
    public static CurrentUser? GetCurrentUser(this HttpContext context)
    {
         if (context.User.Identity is { IsAuthenticated: true })
            {
                var claims = context.User;
                var userId = Guid.Parse(claims.FindFirstValue(ClaimTypes.NameIdentifier)!);
               var email = claims.FindFirstValue(ClaimTypes.Email);
               // 你需要从 Claims 中解析出 UserType 和 PlanType，这需要在登录时存入
                // 为了简化，我们这里先用默认值
                var userType = Enum.Parse<UserType>(claims.FindFirstValue("UserType") ?? "Registered");
                var planType = Enum.Parse<UserPlanType>(claims.FindFirstValue("PlanType") ?? "Free");
   
                return new CurrentUser(userId, userType, email, planType, false);
            }
   
            // 2. 如果不是认证用户，尝试获取匿名用户信息
            if (context.Items.TryGetValue(AnonymousUserMiddleware.USER_CONTEXT_KEY, out var anonymousUser))
            {
                return anonymousUser as CurrentUser;
            }
   
            return null;
    }

    /// <summary>
    ///     获取当前用户ID
    /// </summary>
    public static Guid? GetCurrentUserId(this HttpContext context)
    {
        return context.GetCurrentUser()?.UserId;
    }

    /// <summary>
    ///     检查当前用户是否为注册用户
    /// </summary>
    public static bool IsRegisteredUser(this HttpContext context)
    {
        var user = context.GetCurrentUser();
        return user != null && user.IsRegistered;
    }

    /// <summary>
    ///     检查当前用户是否为高级用户
    /// </summary>
    public static bool IsPremiumUser(this HttpContext context)
    {
        var user = context.GetCurrentUser();
        return user != null && user.IsPremium;
    }
}