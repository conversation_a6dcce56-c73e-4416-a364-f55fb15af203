using Shared.Common;

namespace Shared.DTOs;

public record WorkerHardwareInfo(int CpuCores, double TotalMemoryGB, double TotalDiskGB);

public record WorkerRuntimeMetrics(
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    int ActiveTasks,
    int TotalProcessedTasks,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    DateTime LastUpdated);

public record WorkerRegisterRequest(string BaseUrl, string MachineName, WorkerHardwareInfo HardwareInfo, DateTime LastStartAt);

public record WorkerHeartbeat(Guid WorkerId, DateTime Timestamp, WorkerRuntimeMetrics RuntimeMetrics, WorkerStatus Status);

public record WorkerResponse(
    Guid Id,
    string Name,
    string BaseUrl,
    string? MachineName,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    WorkerHardwareInfo? HardwareInfo,
    int TotalProcessedTasks,
    int FailureCount,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    DateTime? LastActiveAt,
    DateTime? LastHealthCheckAt,
    DateTime? LastFailureAt,
    DateTime? LastStartAt);

public record WorkerRestartResult(Guid WorkerId, string WorkerName, string Message, DateTime RequestedAt);

public record WorkerStatsResponse(
    int TotalWorkers,
    int OnlineWorkers,
    int OfflineWorkers,
    int HealthyWorkers,
    int WarningWorkers,
    int CriticalWorkers,
    int TotalProcessedTasks,
    int TotalFailures);

public record WorkerPerformanceResponse(double AverageCpuUsage, double AverageMemoryUsage, double AverageDiskUsage, int TotalActiveTasks, DateTime LastUpdated);

public record AddWorkerNodeRequest(string Name, string BaseUrl);

public record ToggleWorkerNodeRequest(bool IsActive);

public record MaintenanceModeRequest(bool MaintenanceMode);

public record WorkerNodeDeletionSummary(Guid NodeId, string NodeName, int DeletedTasks, int DeletedMetrics, int DeletedAlerts, bool WasForced);

public record NodeHeartbeat(WorkerRuntimeMetrics Metrics, WorkerStatus Status, DateTime Timestamp);

public record WorkerNodeRegistration(string Name, string BaseUrl);

public record RegistrationResponse(bool Success, string? NodeId, string? Error);

public record WorkerTaskAssignmentRequest(Guid TaskId, string TaskType, string Parameters);

public record WorkerCapacityResponse(
    Guid NodeId,
    string NodeName,
    int MaxConcurrentTasks,
    int CurrentActiveTasks,
    int AvailableCapacity,
    double CpuUsagePercent,
    double MemoryUsagePercent,
    bool CanAcceptTasks);

public record ScaleWorkersRequest(int TargetCount);

public class ScaleWorkersRequestValidator : AbstractValidator<ScaleWorkersRequest>
{
    public ScaleWorkersRequestValidator()
    {
        RuleFor(x => x.TargetCount).GreaterThan(0).WithMessage("目标数量必须大于0").LessThanOrEqualTo(100).WithMessage("目标数量不能超过100");
    }
}