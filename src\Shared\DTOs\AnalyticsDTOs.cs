namespace Shared.DTOs;

// ==================== 总体统计 ====================

public record OverviewStatsResponse(UserOverviewStatsResponse Users, TaskOverviewStatsResponse Tasks, SystemOverviewStatsResponse System);

public record UserOverviewStatsResponse(int TotalUsers, int ActiveUsers, int NewUsersToday, double GrowthRate);

public record TaskOverviewStatsResponse(int TotalTasks, int CompletedTasks, int FailedTasks, double SuccessRate);

public record SystemOverviewStatsResponse(int HealthyWorkers, double AverageCpuUsage, double AverageMemoryUsage, int ActiveAlerts);

public record DashboardStatsResponse(Dictionary<string, object> Stats, List<ChartDataResponse> Charts);

public record ChartDataResponse(string Name, string Type, List<DataPointResponse> Data);

public record DataPointResponse(string Label, double Value, DateTime? Timestamp);

// ==================== 用户统计 ====================

public record UserStatsResponse(int TotalUsers, int ActiveUsers, int NewUsers, List<UserGrowthDataResponse> GrowthData);

public record UserGrowthDataResponse(DateTime Date, int NewUsers, int TotalUsers, int ActiveUsers);

public record UserGrowthStatsResponse(double DailyGrowthRate, double WeeklyGrowthRate, double MonthlyGrowthRate, List<UserGrowthDataResponse> GrowthTrend);

public record UserActivityStatsResponse(int DailyActiveUsers, int WeeklyActiveUsers, int MonthlyActiveUsers, List<UserActivityDataResponse> ActivityTrend);

public record UserActivityDataResponse(DateTime Date, int ActiveUsers, double ActivityRate);

public record UserRetentionStatsResponse(double Day1Retention, double Day7Retention, double Day30Retention, List<RetentionCohortResponse> Cohorts);

public record RetentionCohortResponse(DateTime CohortDate, int InitialUsers, Dictionary<int, double> RetentionRates);

// ==================== 任务统计 ====================

public record TaskStatsResponse(
    int TotalTasks,
    int CompletedTasks,
    int FailedTasks,
    int PendingTasks,
    double SuccessRate,
    List<TaskStatsDataResponse> StatsData);

public record TaskStatsDataResponse(DateTime Date, int TotalTasks, int CompletedTasks, int FailedTasks, double SuccessRate);

public record TaskPerformanceStatsResponse(
    double AverageProcessingTime,
    double MedianProcessingTime,
    double P95ProcessingTime,
    List<TaskPerformanceDataResponse> PerformanceData);

public record TaskPerformanceDataResponse(DateTime Date, double AverageTime, double MedianTime, double P95Time);

public record TaskSuccessRateStatsResponse(
    double OverallSuccessRate,
    Dictionary<string, double> SuccessRateByType,
    List<SuccessRateDataResponse> SuccessRateData);

public record SuccessRateDataResponse(DateTime Date, double SuccessRate, int TotalTasks, int SuccessfulTasks);

public record TaskTypeStatsResponse(Dictionary<string, int> TaskCountByType, Dictionary<string, double> SuccessRateByType, List<TaskTypeDataResponse> TypeData);

public record TaskTypeDataResponse(string TaskType, int Count, double SuccessRate, double AverageTime);

// ==================== 下载统计 ====================

public record DownloadStatsResponse(long TotalDownloads, long TotalSize, double AverageSize, List<DownloadStatsDataResponse> StatsData);

public record DownloadStatsDataResponse(DateTime Date, int DownloadCount, long TotalSize, double AverageSize);

public record DownloadVolumeStatsResponse(long TotalVolume, long DailyVolume, long WeeklyVolume, long MonthlyVolume, List<VolumeDataResponse> VolumeData);

public record VolumeDataResponse(DateTime Date, long Volume, int FileCount);

public record PopularContentStatsResponse(
    List<PopularContentItemResponse> PopularVideos,
    List<PopularContentItemResponse> PopularChannels,
    List<PopularContentItemResponse> PopularPlaylists);

public record PopularContentItemResponse(string Id, string Title, int DownloadCount, DateTime LastDownloaded);

// ==================== 报表生成 ====================

public record GenerateReportRequest(string ReportType, DateTime StartDate, DateTime EndDate, Dictionary<string, object>? Parameters);

public record ReportResponse(Guid Id, string Name, string Type, string Status, DateTime CreatedAt, DateTime? CompletedAt, string? DownloadUrl);