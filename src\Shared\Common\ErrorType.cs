namespace Shared.Common;

/// <summary>
/// 使用强类型的枚举代替字符串常量，提供编译时安全。
/// </summary>
public enum ErrorType
{
    // ... (内容和之前一样，此处省略)
    None = 0, Internal = 1, Validation = 2, Unauthorized = 3, Forbidden = 4, NotFound = 5, Conflict = 6, TooManyRequests = 7, InvalidArgument = 8, InvalidOperation = 9, NotSupported = 10, Timeout = 11, OperationCancelled = 12, NetworkError = 13, HttpError = 14, JsonError = 15, InsufficientMemory = 16,
    UserNotFound = 100, UserEmailExists = 101, UserDisabled = 102, UserQuotaExceeded = 103, PlanQuotaExceeded = 104,
    AuthTokenInvalid = 110, AuthTokenExpired = 111, AuthCredentialsInvalid = 112,
    TaskNotFound = 200, TaskAlreadyStarted = 201, TaskAlreadyCompleted = 202, TaskCancelled = 203, TaskFailed = 204, TaskQueueFull = 205, UnsupportedTaskType = 206, BatchTaskNotFound = 207,
    YouTubeVideoNotFound = 300, YouTubeVideoPrivate = 301, YouTubeVideoUnavailable = 302, YouTubePlaylistNotFound = 303, YouTubeChannelNotFound = 304, YouTubeRateLimited = 305, YouTubeApiError = 306, InvalidVideoId = 310, InvalidPlaylistId = 311, InvalidChannelId = 312, InvalidUrl = 313, InvalidUrlFormat = 314,
    WorkerNotFound = 400, WorkerUnavailable = 401, WorkerOverloaded = 402, WorkerProcessingError = 403,
    FileNotFound = 500, FileTooLarge = 501, FileExpired = 502, FileProcessingError = 503, FileAccessDenied = 504, DirectoryNotFound = 505,
    ProxyNotAvailable = 600, ProxyConnectionFailed = 601, ProxyRateLimited = 602,
}

