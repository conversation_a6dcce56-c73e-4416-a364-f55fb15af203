using Shared.Common;

namespace Shared.DTOs;

public record WorkerOverviewResponse(
    Guid Id,
    string Name,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    DateTime? LastActiveAt,
    double? CurrentCpuUsage,
    double? CurrentMemoryUsage,
    double? CurrentDiskUsage,
    int ActiveTasks,
    int ActiveAlertsCount);

public record SystemOverviewResponse(
    int TotalWorkers,
    int OnlineWorkers,
    int OfflineWorkers,
    int HealthyWorkers,
    int WarningWorkers,
    int CriticalWorkers,
    int TotalActiveTasks,
    int TotalActiveAlerts,
    double AverageCpuUsage,
    double AverageMemoryUsage,
    double AverageDiskUsage);

public record WorkerMetricsResponse(
    Guid WorkerId,
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    int ActiveTasks,
    int TotalProcessedTasks,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    DateTime RecordedAt);

public record WorkerHistoryResponse(Guid WorkerId, DateTime StartTime, DateTime EndTime, List<MetricsDataPoint> DataPoints);

public record MetricsDataPoint(DateTime Timestamp, double CpuUsage, double MemoryUsage, double DiskUsage, int ActiveTasks);

public record WorkerAlertResponse(
    Guid Id,
    Guid WorkerId,
    string WorkerName,
    WorkerAlertType AlertType,
    WorkerAlertLevel AlertLevel,
    string Title,
    string Message,
    bool IsResolved,
    DateTime CreatedAt,
    DateTime? ResolvedAt);

public record AlertSummaryResponse(
    int TotalAlerts,
    int UnresolvedAlerts,
    int CriticalAlerts,
    int WarningAlerts,
    int InfoAlerts,
    List<WorkerAlertResponse> RecentAlerts);

public record WorkerMetricsResponse(
    Guid Id,
    Guid WorkerId,
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    int ActiveTasks,
    DateTime RecordedAt);

public record DashboardDataResponse(
    SystemOverviewResponse SystemOverview,
    List<WorkerOverviewResponse> WorkersOverview,
    AlertSummaryResponse AlertSummary,
    DateTime LastUpdated);

public record WorkerRealtimeDataResponse(
    Guid WorkerId,
    string WorkerName,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    DateTime? LastActiveAt,
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    int CurrentTasks,
    int ActiveAlertsCount,
    DateTime LastMetricsUpdate);

public record PagedResponse<T>(List<T> Items, int TotalCount, int Page, int PageSize)
{
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}

public record PerformanceSummaryResponse(
    double Avg24hCpuUsage,
    double Avg24hMemoryUsage,
    double Avg24hDiskUsage,
    double Max24hCpuUsage,
    double Max24hMemoryUsage,
    double Max24hDiskUsage,
    double Avg7dCpuUsage,
    double Avg7dMemoryUsage,
    double Avg7dDiskUsage,
    DateTime LastUpdated);

public record PerformanceTrendsResponse(DateTime StartTime, DateTime EndTime, List<PerformanceTrendPoint> TrendPoints);

public record PerformanceTrendPoint(DateTime Timestamp, double CpuUsage, double MemoryUsage, double DiskUsage, int ActiveTasks);

public record RealtimeStatsResponse(
    int ActiveWorkers,
    int ActiveTasks,
    int PendingTasks,
    int RecentCompletedTasks,
    int RecentFailedTasks,
    int UnresolvedAlerts,
    double CurrentCpuUsage,
    double CurrentMemoryUsage,
    double CurrentDiskUsage,
    double TotalNetworkReceived,
    double TotalNetworkSent,
    DateTime Timestamp);

public record ExportMetricsRequest(DateTime StartTime, DateTime EndTime, List<Guid>? WorkerIds, string Format);

public record UpdateThresholdsRequest(
    double CpuWarningThreshold,
    double CpuCriticalThreshold,
    double MemoryWarningThreshold,
    double MemoryCriticalThreshold,
    double DiskWarningThreshold,
    double DiskCriticalThreshold);