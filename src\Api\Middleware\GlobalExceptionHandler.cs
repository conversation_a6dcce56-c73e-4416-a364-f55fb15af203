using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Shared.Common;

namespace Api.Middleware;

/// <summary>
/// 全局异常处理器。
/// 仅用于捕获未处理的、非预期的系统异常，并统一返回 HTTP 500 Internal Server Error。
/// 业务逻辑错误和验证错误应在各自的层级处理，并通过 ServiceResult 和 ApiResponse 返回。
/// </summary>
public class GlobalExceptionHandler : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 尝试处理发生的异常。
    /// </summary>
    /// <param name="httpContext">当前 HTTP 上下文。</param>
    /// <param name="exception">捕获到的异常。</param>
    /// <param name="cancellationToken">取消令牌。</param>
    /// <returns>如果异常被处理，返回 true；否则返回 false。</returns>
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        // 记录所有未处理的异常，这对于调试和监控至关重要。
        _logger.LogError(exception, "An unhandled exception occurred during request processing.");

        // 统一返回 HTTP 500 Internal Server Error。
        httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        httpContext.Response.ContentType = "application/json";

        // 创建一个通用的、不暴露内部细节的错误响应。
        var errorDetails = new ErrorDetails(
            Message: "An unexpected internal server error occurred. Please try again later.",
            Code: ErrorType.Internal.ToString() // 使用我们定义的 ErrorType.Internal
        );
        var apiResponse = ApiResponse.Failure(errorDetails);

        // 将 ApiResponse 序列化为 JSON 并写入响应体。
        var json = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase // 保持 JSON 属性名为 camelCase
        });

        await httpContext.Response.WriteAsync(json, cancellationToken);

        // 返回 true 表示我们已经处理了异常，不需要其他异常处理器介入。
        return true;
    }
}