﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public class YouTubeVideo
{
    public required string VideoId { get; init; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public required string ChannelId { get; set; }
    public required string ChannelName { get; set; }
    public int Duration { get; set; }
    public long? ViewCount { get; set; }
    public long? LikeCount { get; set; }
    public long? CommentCount { get; set; }
    public DateTime? UploadDate { get; set; }
    public string? Thumbnail { get; set; }
    public required string VideoStreamsJson { get; set; }
    public required string AudioStreamsJson { get; set; }
    public required string SubtitlesJson { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class YouTubeVideoConfiguration : IEntityTypeConfiguration<YouTubeVideo>
{
    public void Configure(EntityTypeBuilder<YouTubeVideo> builder)
    {
        builder.ToTable("youtube_videos", "public");
        builder.HasKey(v => v.VideoId);
        builder.Property(v => v.VideoId).IsRequired().HasMaxLength(20).HasColumnName("video_id");
        builder.Property(v => v.Title).IsRequired().HasMaxLength(500).HasColumnName("title");
        builder.Property(v => v.Description).HasColumnType("text").HasColumnName("description");
        builder.Property(v => v.ChannelId).IsRequired().HasMaxLength(50).HasColumnName("channel_id");
        builder.Property(v => v.ChannelName).IsRequired().HasMaxLength(255).HasColumnName("channel_name");
        builder.Property(v => v.Duration).HasColumnName("duration");
        builder.Property(v => v.ViewCount).HasColumnName("view_count");
        builder.Property(v => v.LikeCount).HasColumnName("like_count");
        builder.Property(v => v.CommentCount).HasColumnName("comment_count");
        builder.Property(v => v.UploadDate).HasColumnName("upload_date");
        builder.Property(v => v.Thumbnail).HasMaxLength(500).HasColumnName("thumbnail");
        builder.Property(v => v.VideoStreamsJson).IsRequired().HasColumnType("jsonb").HasColumnName("video_streams_json");
        builder.Property(v => v.AudioStreamsJson).IsRequired().HasColumnType("jsonb").HasColumnName("audio_streams_json");
        builder.Property(v => v.SubtitlesJson).IsRequired().HasColumnType("jsonb").HasColumnName("subtitles_json");
        builder.Property(v => v.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(v => v.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(v => v.ExpiresAt).HasColumnName("expires_at");

        builder.HasIndex(v => v.ChannelId).HasDatabaseName("ix_youtube_videos_channel_id");
        builder.HasIndex(v => v.UploadDate).HasDatabaseName("ix_youtube_videos_upload_date");
        builder.HasIndex(v => v.ExpiresAt).HasDatabaseName("ix_youtube_videos_expires_at");
        builder.HasIndex(v => v.CreatedAt).HasDatabaseName("ix_youtube_videos_created_at");
    }
}