namespace Shared.Common;

public record ApiResponse(ErrorDetails? Error = null)
{
    public bool IsSuccess => Error is null;

    public static ApiResponse Failure(ErrorDetails error) => new(error);
    public static ApiResponse Success() => new();
}

public record ApiResponse<T>(T? Data, ErrorDetails? Error = null) : ApiResponse(Error)
{
    public new static ApiResponse<T> Failure(ErrorDetails error) => new(default, error);
    public static ApiResponse<T> Success(T data) => new(data);
}

public record ErrorDetails(string Message, string? Code = null, List<ValidationError>? ValidationErrors = null);
