namespace Shared.Common;

/// <summary>
/// Represents the result of a business operation, without a data payload.
/// </summary>
/// <param name="Error">The Error object containing details. If Error.None, the operation was successful.</param>
/// <param name="ValidationErrors">A list of validation errors, if any.</param>
public record ServiceResult(Error Error, List<ValidationError>? ValidationErrors = null)
{
    /// <summary>
    /// Indicates if the operation was successful. Derived from the Error's type.
    /// </summary>
    public bool IsSuccess => Error.Type == ErrorType.None;

    // A default constructor for convenience, representing a successful result.
    public ServiceResult() : this(Error.None, null) { }

    /// <summary>
    /// Creates a successful ServiceResult.
    /// </summary>
    public static ServiceResult Success() => new();

    /// <summary>
    /// Creates a failed ServiceResult.
    /// </summary>
    /// <param name="error">The error that occurred. Must not be of type None.</param>
    /// <exception cref="ArgumentException">Thrown if the provided error's type is None.</exception>
    public static ServiceResult Failure(Error error)
    {
        if (error.Type == ErrorType.None)
        {
            throw new ArgumentException("Cannot create a failure result with an error of type None.", nameof(error));
        }
        return new(error, null);
    }

    /// <summary>
    /// Creates a validation-failed ServiceResult.
    /// </summary>
    public static ServiceResult ValidationError(List<ValidationError> errors)
        => new(new Error(ErrorType.Validation, "Input validation failed."), errors);
}

/// <summary>
/// Represents the result of a business operation, with a generic data payload.
/// </summary>
/// <param name="Data">The data payload, if the operation was successful.</param>
/// <param name="Error">The Error object containing details. If Error.None, the operation was successful.</param>
/// <param name="ValidationErrors">A list of validation errors, if any.</param>
public record ServiceResult<T>(T? Data, Error Error, List<ValidationError>? ValidationErrors = null) 
    : ServiceResult(Error, ValidationErrors)
{
    /// <summary>
    /// Creates a successful ServiceResult with a data payload.
    /// </summary>
    public static ServiceResult<T> Success(T data) => new(data, Error.None, null);

    /// <summary>
    /// Creates a failed ServiceResult<T>. The Data property will be default.
    /// </summary>
    /// <param name="error">The error that occurred. Must not be of type None.</param>
    /// <exception cref="ArgumentException">Thrown if the provided error's type is None.</exception>
    public new static ServiceResult<T> Failure(Error error)
    {
        if (error.Type == ErrorType.None)
        {
            throw new ArgumentException("Cannot create a failure result with an error of type None.", nameof(error));
        }
        return new(default, error, null);
    }

    /// <summary>
    /// Creates a validation-failed ServiceResult<T>. The Data property will be default.
    /// </summary>
    public new static ServiceResult<T> ValidationError(List<ValidationError> errors)
        => new(default, new Error(ErrorType.Validation, "Input validation failed."), errors);

    /// <summary>
    /// Allows implicit conversion from a type T to a successful ServiceResult<T>.
    /// </summary>
    public static implicit operator ServiceResult<T>(T data) => new(data, Error.None, null);
}



/// <summary>
/// 一个结构化的错误对象，使用 C# 12 主构造函数以达到最简语法。
/// </summary>
/// <param name="Type">错误的类型，用于程序化处理。</param>
/// <param name="DeveloperMessage">（可选）面向开发者的详细错误信息，用于日志和调试。</param>
public readonly record struct Error(ErrorType Type, string? DeveloperMessage = null)
{
    /// <summary>
    /// 一个静态实例，代表“无错误”状态。
    /// </summary>
    public static readonly Error None = new(ErrorType.None);
}

/// <summary>
/// Defines the structure for a single field validation error.
/// </summary>
/// <param name="Field">The name of the field that failed validation.</param>
/// <param name="Message">The specific error message for the field.</param>
public record ValidationError(string Field, string Message);