namespace Shared.Common;

public readonly record struct Error(ErrorType Type, string? DeveloperMessage = null)
{
    public static readonly Error None = new(ErrorType.None);
}

public record ServiceResult(Error Error, List<ValidationError>? ValidationErrors = null)
{
    public bool IsSuccess => Error.Type == ErrorType.None;

    public ServiceResult() : this(Error.None, null) { }

    public static ServiceResult Failure(Error error)
    {
        if (error.Type == ErrorType.None)
        {
            throw new ArgumentException("不能使用None类型的错误创建失败结果", nameof(error));
        }
        return new(error, null);
    }

    public static ServiceResult Success() => new();

    public static ServiceResult ValidationError(List<ValidationError> errors)
        => new(new Error(ErrorType.Validation, "输入验证失败"), errors);
}

public record ServiceResult<T>(T? Data, Error Error, List<ValidationError>? ValidationErrors = null) 
    : ServiceResult(Error, ValidationErrors)
{
    public new static ServiceResult<T> Failure(Error error)
    {
        if (error.Type == ErrorType.None)
        {
            throw new ArgumentException("不能使用None类型的错误创建失败结果", nameof(error));
        }
        return new(default, error, null);
    }

    public static ServiceResult<T> Success(T data) => new(data, Error.None, null);

    public new static ServiceResult<T> ValidationError(List<ValidationError> errors)
        => new(default, new Error(ErrorType.Validation, "输入验证失败"), errors);

    public static implicit operator ServiceResult<T>(T data) => new(data, Error.None, null);
}

public record ValidationError(string Field, string Message);
