using System.Text.Json;
using Api.Data.Models;
using Shared.DTOs;

namespace Api.Extensions;

/// <summary>
///     实体与DTO转换扩展方法 - 严格按照代码规范手动映射
///     禁止使用AutoMapper，必须手动编写转换逻辑
/// </summary>
public static class EntityMappingExtensions
{
    /// <summary>
    ///     WorkerTask实体转换为WorkerTaskResponse DTO
    ///     只选择客户端所需字段，忽略敏感/不必要字段
    /// </summary>
    public static WorkerTaskResponse ToResponse(this WorkerTask task)
    {
        return new WorkerTaskResponse(task.Id, task.UserId, task.BatchTaskId, task.Name, task.TaskType, task.VideoId, task.VideoTitle ?? string.Empty,
            task.VideoUrl ?? string.Empty, task.Status, task.Progress, task.Priority, task.OutputFormat, task.Quality, task.StartTime, task.EndTime,
            task.ResultPath, task.FileSize, task.ErrorMessage, task.CreatedAt, task.StartedAt, task.CompletedAt, task.FileExpiresAt);
    }

    /// <summary>
    ///     BatchTask实体转换为BatchTaskResponse DTO
    /// </summary>
    public static BatchTaskResponse ToResponse(this BatchTask batchTask)
    {
        return new BatchTaskResponse(batchTask.Id, batchTask.UserId, batchTask.Name, batchTask.SourceType, batchTask.SourceId, batchTask.SourceUrl,
            batchTask.SourceTitle ?? string.Empty, batchTask.TotalVideoCount, batchTask.SelectedVideoCount, batchTask.Status, batchTask.Progress,
            batchTask.CompletedTaskCount, batchTask.FailedTaskCount, batchTask.CreatedAt, batchTask.StartedAt, batchTask.CompletedAt,
            batchTask.WorkerTasks.Select(t => t.ToResponse()).ToList());
    }

    /// <summary>
    ///     User实体转换为UserResponse DTO
    ///     严格过滤敏感字段如PasswordHash
    /// </summary>
    public static UserResponse ToResponse(this User user)
    {
        return new UserResponse(user.Id, user.UserType, user.Email, // 可能为null（匿名用户）
            user.PlanType, user.PlanExpiresAt, user.Status, user.CreatedAt, user.LastActiveAt);
    }

    /// <summary>
    ///     CreateVideoTaskRequest DTO转换为WorkerTask实体
    ///     处理可空性差异，提供合理默认值
    /// </summary>
    public static WorkerTask ToEntity(this CreateVideoTaskRequest request, Guid userId)
    {
        return new WorkerTask
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Name = $"视频下载 - {request.VideoId}",
            TaskType = WorkerTaskType.VideoDownload,
            VideoId = request.VideoId,
            Parameters = JsonSerializer.Serialize(new
            {
                request.OutputFormat,
                request.Quality,
                request.StartTime,
                request.EndTime
            }),
            Status = WorkerTaskStatus.Pending,
            Progress = 0,
            Priority = WorkerTaskPriority.Normal,
            RetryCount = 0,
            MaxRetries = 3,
            OutputFormat = request.OutputFormat ?? "mp4",
            Quality = request.Quality ?? "720p",
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    ///     CreateAudioTaskRequest DTO转换为WorkerTask实体
    /// </summary>
    public static WorkerTask ToEntity(this CreateAudioTaskRequest request, Guid userId)
    {
        return new WorkerTask
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Name = $"音频转换 - {request.VideoId}",
            TaskType = WorkerTaskType.AudioConvert,
            VideoId = request.VideoId,
            Parameters = JsonSerializer.Serialize(new
            {
                request.OutputFormat,
                request.Quality,
                request.StartTime,
                request.EndTime
            }),
            Status = WorkerTaskStatus.Pending,
            Progress = 0,
            Priority = WorkerTaskPriority.Normal,
            RetryCount = 0,
            MaxRetries = 3,
            OutputFormat = request.OutputFormat,
            Quality = request.Quality ?? "128k",
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    ///     CreateGifTaskRequest DTO转换为WorkerTask实体
    /// </summary>
    public static WorkerTask ToEntity(this CreateGifTaskRequest request, Guid userId)
    {
        return new WorkerTask
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Name = $"GIF制作 - {request.VideoId}",
            TaskType = WorkerTaskType.GifCreate,
            VideoId = request.VideoId,
            Parameters = JsonSerializer.Serialize(new
            {
                request.StartTime,
                request.EndTime,
                Fps = request.Fps ?? 10,
                Width = request.Width ?? 400
            }),
            Status = WorkerTaskStatus.Pending,
            Progress = 0,
            Priority = WorkerTaskPriority.Normal,
            RetryCount = 0,
            MaxRetries = 3,
            OutputFormat = "gif",
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    ///     CreateBatchTaskRequest DTO转换为BatchTask实体
    /// </summary>
    public static BatchTask ToEntity(this CreateBatchTaskRequest request, Guid userId)
    {
        return new BatchTask
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Name = request.Name,
            SourceType = request.SourceType,
            SourceId = request.SourceId,
            SourceUrl = request.SourceUrl,
            TotalVideoCount = request.SelectedVideoIds.Count,
            SelectedVideoCount = request.SelectedVideoIds.Count,
            Configuration = request.Configuration,
            Status = BatchTaskStatus.Created,
            Progress = 0,
            CompletedTaskCount = 0,
            FailedTaskCount = 0,
            CancelledTaskCount = 0,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    ///     LINQ Select投影 - 从数据库查询直接投影到DTO
    ///     用于只读操作，避免加载完整实体
    /// </summary>
    public static IQueryable<WorkerTaskResponse> ProjectToResponse(this IQueryable<WorkerTask> query)
    {
        return query.Select(task => new WorkerTaskResponse(task.Id, task.UserId, task.BatchTaskId, task.Name, task.TaskType, task.VideoId,
            task.VideoTitle ?? string.Empty, task.VideoUrl ?? string.Empty, task.Status, task.Progress, task.Priority, task.OutputFormat, task.Quality,
            task.StartTime, task.EndTime, task.ResultPath, task.FileSize, task.ErrorMessage, task.CreatedAt, task.StartedAt, task.CompletedAt,
            task.FileExpiresAt));
    }

    /// <summary>
    ///     批量任务LINQ投影
    /// </summary>
    public static IQueryable<BatchTaskResponse> ProjectToResponse(this IQueryable<BatchTask> query)
    {
        return query.Select(batch => new BatchTaskResponse(batch.Id, batch.UserId, batch.Name, batch.SourceType, batch.SourceId, batch.SourceUrl,
            batch.SourceTitle ?? string.Empty, batch.TotalVideoCount, batch.SelectedVideoCount, batch.Status, batch.Progress, batch.CompletedTaskCount,
            batch.FailedTaskCount, batch.CreatedAt, batch.StartedAt, batch.CompletedAt, batch.WorkerTasks.Select(t => t.ToResponse()).ToList()));
    }
}