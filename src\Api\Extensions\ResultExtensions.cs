using System.Net;
using Microsoft.AspNetCore.Http.HttpResults;
using Shared.Common;

namespace Api.Extensions;

/// <summary>
/// A static class containing extension methods to convert ServiceResult objects
/// into standard IResult (HTTP) responses, bridging the service and API layers.
/// </summary>
public static class ResultExtensions
{
    // The core translation dictionary. It maps a business-layer ErrorType to its
    // corresponding API-layer representation (HTTP Status Code and a default user-friendly message).
    private static readonly IReadOnlyDictionary<ErrorType, (HttpStatusCode StatusCode, string DefaultMessage)> ErrorMapping = 
        new Dictionary<ErrorType, (HttpStatusCode, string)>
    {
        [ErrorType.Validation] = (HttpStatusCode.UnprocessableEntity, "One or more validation errors occurred."),
        [ErrorType.NotFound] = (HttpStatusCode.NotFound, "The requested resource was not found."),
        [ErrorType.UserNotFound] = (HttpStatusCode.NotFound, "The specified user was not found."),
        [ErrorType.TaskNotFound] = (HttpStatusCode.NotFound, "The specified task was not found."),
        [ErrorType.Conflict] = (HttpStatusCode.Conflict, "A conflict occurred with the current state of the resource."),
        [ErrorType.UserEmailExists] = (HttpStatusCode.Conflict, "This email address is already in use."),
        [ErrorType.Forbidden] = (HttpStatusCode.Forbidden, "You do not have permission to perform this action."),
        [ErrorType.Unauthorized] = (HttpStatusCode.Unauthorized, "You are not authorized to perform this action."),
        [ErrorType.AuthCredentialsInvalid] = (HttpStatusCode.Unauthorized, "Invalid email or password."),
        [ErrorType.Internal] = (HttpStatusCode.InternalServerError, "An unexpected internal server error occurred."),
        [ErrorType.InvalidArgument] = (HttpStatusCode.BadRequest, "One or more arguments provided are invalid.")
        // Add other specific mappings here...
    };

    /// <summary>
    /// Converts a ServiceResult into an IResult, creating a standard HTTP response.
    /// </summary>
    /// <param name="result">The ServiceResult from the business layer.</param>
    /// <param name="successStatusCode">The HTTP status code to return on success. Defaults to 200 OK.</param>
    public static IResult ToHttpResult(this ServiceResult result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess
            ? CreateSuccessResult(successStatusCode)
            : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    /// <summary>
    /// Converts a generic ServiceResult<T> into an IResult, creating a standard HTTP response with a data body.
    /// </summary>
    /// <typeparam name="T">The type of the data in the result.</typeparam>
    /// <param name="result">The ServiceResult<T> from the business layer.</param>
    /// <param name="successStatusCode">The HTTP status code to return on success. Defaults to 200 OK.</param>
    public static IResult ToHttpResult<T>(this ServiceResult<T> result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess
            ? CreateSuccessResult(result.Data, successStatusCode)
            : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    private static IResult CreateSuccessResult(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(ApiResponse.Success()),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, ApiResponse.Success()),
            HttpStatusCode.NoContent => TypedResults.NoContent(),
            _ => TypedResults.Ok(ApiResponse.Success())
        };
    }

    private static IResult CreateSuccessResult<T>(T? data, HttpStatusCode statusCode)
    {
        if (data is null)
        {
            return TypedResults.NoContent();
        }

        var response = ApiResponse<T>.Success(data);
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(response),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, response),
            _ => TypedResults.Ok(response)
        };
    }

    private static IResult ConvertErrorToHttpResult(Error error, List<ValidationError>? validationErrors)
    {
        // Look up the error type in our mapping, or fall back to a default.
        var (statusCode, defaultMessage) = ErrorMapping.GetValueOrDefault(
            error.Type, 
            (HttpStatusCode.BadRequest, "A bad request was made."));

        // Create the standardized ErrorDetails object for the API response.
        var errorDetails = new ErrorDetails(
            Message: defaultMessage,
            Code: error.Type.ToString(),
            ValidationErrors: validationErrors
        );

        // Create the final ApiResponse.
        var response = ApiResponse.Failure(errorDetails);

        // Return the appropriate IResult based on the mapped status code.
        return statusCode switch
        {
            HttpStatusCode.NotFound => TypedResults.NotFound(response),
            HttpStatusCode.Conflict => TypedResults.Conflict(response),
            HttpStatusCode.Forbidden => TypedResults.Json(response, statusCode: (int)HttpStatusCode.Forbidden),
            HttpStatusCode.Unauthorized => TypedResults.Unauthorized(), // 401 should not have a body.
            HttpStatusCode.UnprocessableEntity => TypedResults.UnprocessableEntity(response),
            HttpStatusCode.InternalServerError => TypedResults.Problem(detail: response.Error?.Message, statusCode: (int)statusCode),
            _ => TypedResults.BadRequest(response)
        };
    }
}