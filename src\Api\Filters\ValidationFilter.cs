using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Shared.Common;

namespace Api.Filters;

/// <summary>
/// 验证过滤器 - 自动验证请求DTO。
/// 验证失败时，直接返回 HTTP 422 Unprocessable Entity 响应，而不是抛出异常。
/// </summary>
/// <typeparam name="T">要验证的DTO类型。</typeparam>
public class ValidationFilter<T> : IEndpointFilter where T : class
{
    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        // 1. 尝试从请求参数中获取指定类型的 DTO 实例。
        //    Minimal API 会自动将请求体绑定到 DTO 参数上。
        var argument = context.Arguments.OfType<T>().FirstOrDefault();

        // 2. 如果没有找到指定类型的 DTO 参数，或者参数为 null，则直接跳过验证。
        //    这通常发生在 GET 请求或没有请求体的 POST/PUT 请求上。
        if (argument == null)
        {
            return await next(context);
        }

        // 3. 从 DI 容器中获取对应 DTO 的验证器。
        //    如果未注册验证器，也直接跳过（这通常意味着该 DTO 不需要验证）。
        var validator = context.HttpContext.RequestServices.GetService<IValidator<T>>();
        if (validator == null)
        {
            return await next(context);
        }

        // 4. 执行验证。
        var validationResult = await validator.ValidateAsync(argument);

        // 5. 如果验证失败，直接构建并返回 HTTP 422 响应。
        if (!validationResult.IsValid)
        {
            // 将 FluentValidation 的 ValidationFailure 转换为我们定义的 ValidationError 列表。
            var errors = validationResult.Errors
                .Select(error => new ValidationError(error.PropertyName, error.ErrorMessage))
                .ToList();

            // 构建 ErrorDetails 对象，包含验证错误信息。
            var errorDetails = new ErrorDetails(
                Message: "One or more validation errors occurred.",
                Code: ErrorType.Validation.ToString(), // 使用我们定义的 ErrorType.Validation
                ValidationErrors: errors
            );

            // 构建 ApiResponse，并返回 HTTP 422 Unprocessable Entity。
            return TypedResults.UnprocessableEntity(ApiResponse.Failure(errorDetails));
        }

        // 6. 如果验证成功，继续执行管道中的下一个过滤器或最终的端点处理程序。
        return await next(context);
    }
}

/// <summary>
/// 验证过滤器扩展方法。
/// 简化在 Minimal API 端点中应用验证过滤器的语法。
/// </summary>
public static class ValidationFilterExtensions
{
    /// <summary>
    /// 为单个路由处理程序添加验证过滤器。
    /// </summary>
    /// <typeparam name="TDto">要验证的DTO类型。</typeparam>
    public static RouteHandlerBuilder WithValidation<TDto>(this RouteHandlerBuilder builder) where TDto : class
    {
        return builder.AddEndpointFilter<ValidationFilter<TDto>>();
    }
}
