﻿using System.Security.Claims;
using Api.Data.Models;
using Api.Extensions;
using Api.Filters;
using Api.Middleware;
using Api.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AuthEndpoints
{
    public static void MapAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/auth").WithTags("Auth");

        group.MapPost("/register", async Task<IResult> (RegisterRequest request, AuthService authService, HttpContext context) =>
        {
            var anonymousUserId = context.GetCurrentUser()?.IsAnonymous == true ? context.GetCurrentUserId() : null;
            var result = await authService.RegisterAsync(request, anonymousUserId);
            if (result.IsSuccess)
            {
                 if (anonymousUserId.HasValue)
           {
               context.Response.Cookies.Delete(AnonymousUserMiddleware.ANONYMOUS_COOKIE_NAME);
           }
   
            // 【重要】注册成功后，我们应该直接让用户登录
            // 这需要复用 /login 路由中的登录逻辑
            var user = result.Data!;
            var session = await authService.CreateSessionAsync(
                user.Id,
                context.Connection.RemoteIpAddress?.ToString(),
                context.Request.Headers.UserAgent.ToString()
            );
   
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.Email ?? string.Empty),
                new("SessionToken", session.SessionToken),
               new("UserType", user.UserType.ToString()),
               new("PlanType", user.PlanType.ToString())
           };
   
            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
           var authProperties = new AuthenticationProperties
           {
               IsPersistent = true,
                ExpiresUtc = session.ExpiresAt
            };
  
           await context.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
               new ClaimsPrincipal(claimsIdentity),
               authProperties);
                return ResultExtensions.ApiOk(result.Data!, "注册成功");
            }

            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("用户注册").WithDescription("创建新用户账户，支持从匿名用户转换为正式用户");

        group.MapPost("/login", async Task<IResult> (LoginRequest request, AuthService authService, HttpContext context) =>
        {
            var result = await authService.LoginAsync(request);
            if (result.IsSuccess)
            {
                 var user = result.Data!.User;
                var session = await authService.CreateSessionAsync(user.Id, context.Connection.RemoteIpAddress?.ToString(), context.Request.Headers.UserAgent.ToString());
                var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
           new(ClaimTypes.Name, user.Email ?? string.Empty),
           new(ClaimTypes.Email, result.Data.User.Email ?? string.Empty),
            new("UserType", result.Data.User.UserType.ToString()),
             new("UserType", user.UserType.ToString()),
        new("PlanType", user.PlanType.ToString())
            // 【关键】将数据库会话令牌存入 Claim
                    new("SessionToken", session.SessionToken)
        };
 var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties
               {
                   // 允许持久化 Cookie，这样关闭浏览器后依然保持登录状态
                   IsPersistent = true,
                   ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30)
              };
   
              // 调用 SignInAsync，ASP.NET Core 会自动创建加密的 HttpOnly Cookie 并发送给客户端
              await context.SignInAsync(
                  CookieAuthenticationDefaults.AuthenticationScheme,
                   new ClaimsPrincipal(claimsIdentity),
                   authProperties);
                return ResultExtensions.ApiOk(result.Data!, result.Data!.Message);
            }

            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("用户登录").WithDescription("用户身份验证登录，支持从匿名用户转换");

        group.MapPost("/logout", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
             var sessionToken = user.FindFirstValue("SessionToken");
        if (sessionToken != null)
       {
           // 从数据库中撤销此会话
            await authService.RevokeSessionAsync(sessionToken);
        }
            await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return ResultExtensions.ApiOk("登出成功");
        }).WithSummary("用户登出").WithDescription("退出登录并清除用户会话").RequireAuthorization();

        group.MapGet("/check", IResult (HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiOk(new { authenticated = false, userType = "none" });
            return ResultExtensions.ApiOk(new
            {
                authenticated = true,
                userType = currentUser.UserType.ToString().ToLower(),
                isAnonymous = currentUser.IsAnonymous,
                isPremium = currentUser.IsPremium
            });
        }).WithSummary("检查认证状态").WithDescription("检查当前用户的认证状态和基本信息");

        group.MapPost("/refresh", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            var result = await authService.GetUserAsync(currentUser.UserId);
            if (!result.IsSuccess) return ResultExtensions.ToHttpResult(result);
            return ResultExtensions.ApiOk(result.Data!, "用户信息已刷新");
        }).WithSummary("刷新用户信息").WithDescription("刷新当前用户的详细信息和状态");

        group.MapGet("/me", async Task<IResult> (AuthService authService, HttpContext context,ClaimsPrincipal user) =>
        {
             var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            var result = await authService.GetUserAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户信息").WithDescription("获取当前登录用户的详细资料和账户信息");

        group.MapGet("/stats", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.GetUserStatsAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计").WithDescription("获取用户的任务统计、下载量和使用情况");

        group.MapPost("/update-profile", async Task<IResult> (UpdateUserRequest request, AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.UpdateUserAsync(currentUser.UserId, request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("更新用户资料").WithDescription("更新当前用户的基本信息和个人资料");

        group.MapPost("/change-password", async Task<IResult> (ChangePasswordRequest request, AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.ChangePasswordAsync(currentUser.UserId, request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("修改密码").WithDescription("修改当前用户的登录密码");

        group.MapPost("/forgot-password", async Task<IResult> (ForgotPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ForgotPasswordAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "如果该邮箱已注册，您将收到密码重置邮件");
        }).WithSummary("忘记密码").WithDescription("发送密码重置邮件到用户注册邮箱");

        group.MapPost("/reset-password", async Task<IResult> (ResetPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ResetPasswordAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "密码重置成功，请重新登录");
        }).WithSummary("重置密码").WithDescription("使用邮件中的重置令牌重置用户密码");

        group.MapPost("/verify-email", async Task<IResult> (VerifyEmailRequest request, AuthService authService) =>
        {
            var result = await authService.VerifyEmailAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "邮箱验证成功");
        }).WithSummary("验证邮箱").WithDescription("使用邮件中的验证令牌验证用户邮箱地址");

        group.MapPost("/resend-verification", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.ResendEmailVerificationAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "验证邮件已发送");
        }).WithSummary("重发验证邮件").WithDescription("重新发送邮箱验证邮件到用户注册邮箱");

        group.MapGet("/sessions", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var sessions = await authService.GetUserActiveSessionsAsync(currentUser.UserId);
            var currentSessionToken = context.Request.Cookies[AuthenticationMiddleware.SESSION_COOKIE_NAME];
            var sessionResponses = sessions.Select(s => new UserSessionResponse(s.Id, s.DeviceInfo, s.IpAddress, s.UserAgent,
                s.SessionToken == currentSessionToken, s.CreatedAt, s.LastAccessedAt, s.ExpiresAt)).ToList();
            return ResultExtensions.ApiOk(sessionResponses);
        }).WithSummary("获取会话列表").WithDescription("获取当前用户的所有活跃登录会话");

        group.MapPost("/sessions/revoke", async Task<IResult> (RevokeSessionRequest request, AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var sessions = await authService.GetUserActiveSessionsAsync(currentUser.UserId);
            var targetSession = sessions.FirstOrDefault(s => s.Id == request.SessionId);
            if (targetSession == null) return ResultExtensions.ApiNotFound("会话不存在");
            var success = await authService.RevokeSessionAsync(targetSession.SessionToken);
            return success ? ResultExtensions.ApiOk("会话已撤销") : ResultExtensions.ApiBadRequest("撤销会话失败");
        }).WithSummary("撤销指定会话").WithDescription("撤销指定ID的用户会话，该设备将需要重新登录");

        group.MapPost("/sessions/revoke-all", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var currentSessionToken = context.Request.Cookies[AuthenticationMiddleware.SESSION_COOKIE_NAME];
            var sessions = await authService.GetUserActiveSessionsAsync(currentUser.UserId);
            var revokedCount = 0;
            foreach (var session in sessions.Where(s => s.SessionToken != currentSessionToken))
            {
                var success = await authService.RevokeSessionAsync(session.SessionToken);
                if (success) revokedCount++;
            }

            return ResultExtensions.ApiOk($"已撤销 {revokedCount} 个会话");
        }).WithSummary("撤销其他会话").WithDescription("撤销除当前会话外的所有其他会话，其他设备需重新登录");

        group.MapGet("/preferences", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.GetUserPreferencesAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户偏好").WithDescription("获取用户的个人偏好设置，如语言、主题、通知等");

        group.MapPost("/preferences", async Task<IResult> (UpdateUserPreferencesRequest request, AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.UpdateUserPreferencesAsync(currentUser.UserId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "偏好设置已更新");
        }).WithSummary("更新用户偏好").WithDescription("更新用户的个人偏好设置和配置");

        group.MapPost("/delete-account", async Task<IResult> (AuthService authService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null || currentUser.IsAnonymous) return ResultExtensions.ApiUnauthorized();
            var result = await authService.DeleteAccountAsync(currentUser.UserId);
            if (result.IsSuccess)
                context.Response.Cookies.Delete(AuthenticationMiddleware.SESSION_COOKIE_NAME);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("删除账户").WithDescription("永久删除用户账户及所有相关数据，此操作不可恢复");
    }

    private static void SetSessionCookie(HttpContext context, string sessionToken)
    {
        var cookieOptions = new CookieOptions
        {
            HttpOnly = true,
            Secure = context.Request.IsHttps,
            SameSite = SameSiteMode.Lax,
            Expires = DateTimeOffset.UtcNow.AddDays(30) // 记住登录状态30天
        };
        context.Response.Cookies.Append(AuthenticationMiddleware.SESSION_COOKIE_NAME, sessionToken, cookieOptions);
    }
}